import os
from unittest.mock import patch, Mock

import pytest

from functions.auth.handler import lambda_handler, _build_allowed_resources
from functions.auth.src.jwt import JWTClaims, AppMetadata, IQResources


ARN_BASE = "arn:aws:execute-api:ap-southeast-2:123456789012:apiId"
METHOD_ARN = f"{ARN_BASE}/dev/GET/some/path"


# Mock the environment variables at module level to ensure they're set before imports
os.environ.setdefault('API_GATEWAY_AUTHORIZER_JWT_DOMAIN', 'https://test.auth0.com/')
os.environ.setdefault('API_GATEWAY_AUTHORIZER_JWT_AUDIENCE', '["https://api.test.com", "https://test.auth0.com/userinfo"]')


def build_claims(org_sites: dict[str, list[str]], sub: str = "test-user") -> JWTClaims:
    app_meta = AppMetadata(
        allowed_sites=[],
        cognito_roles=[],
        iq_data={},
        iq_resources=IQResources(roles=[], sites=org_sites),
        organization=next(iter(org_sites.keys()), ""),
        welcome_email_sent=False,
    )
    # Use the alias for the field name
    claims_data = {
        "sub": sub,
        "email": "<EMAIL>",
        "roles": [],
        "allowed_sites": [],
        "organization": app_meta.organization,
        "https://www.fingermark.tech/databoard/app_metadata": app_meta.model_dump(),
    }
    return JWTClaims(**claims_data)


def test_build_allowed_resources_wildcard_all_sites() -> None:
    claims = build_claims({"org1": ["*"]})
    resources = _build_allowed_resources(METHOD_ARN, claims)

    assert f"{ARN_BASE}/*/*/api/v1/org/org1/*" in resources
    assert f"{ARN_BASE}/*/*/api/v1/org/org1" in resources


@patch("functions.auth.handler.JWTAuthorizer")
def test_lambda_handler_allows_wildcard_org(mock_auth_class) -> None:
    mock_auth = Mock()
    mock_auth_class.return_value = mock_auth

    claims = build_claims({"org1": ["*"]}, sub="sub-1")
    mock_auth.validate_token.return_value = claims

    # Mock the event object with the expected attributes
    event = Mock()
    event.authorization_token = "Bearer valid"
    event.method_arn = METHOD_ARN

    policy = lambda_handler(event, Mock())

    assert policy is not None
    assert policy["principalId"] == "sub-1"
    stmt = policy["policyDocument"]["Statement"][0]
    assert stmt["Effect"] == "Allow"

    res = stmt["Resource"]
    # With multiple resources, Resource is a list
    assert isinstance(res, list)
    assert f"{ARN_BASE}/*/*/api/v1/org/org1/*" in res
    assert f"{ARN_BASE}/*/*/api/v1/org/org1" in res


def test_build_allowed_resources_explicit_sites() -> None:
    claims = build_claims({"org2": ["siteA", "siteB"]})
    resources = _build_allowed_resources(METHOD_ARN, claims)

    # org-level
    assert f"{ARN_BASE}/*/*/api/v1/org/org2" in resources
    assert f"{ARN_BASE}/*/*/api/v1/org/org2/schemas/*" in resources
    assert f"{ARN_BASE}/*/*/api/v1/org/org2/use-cases" in resources
    assert f"{ARN_BASE}/*/*/api/v1/org/org2/use-cases/*" in resources
    assert f"{ARN_BASE}/*/*/api/v1/org/org2/installations" in resources
    assert f"{ARN_BASE}/*/*/api/v1/org/org2/installations/*" in resources

    # site-level
    assert f"{ARN_BASE}/*/*/api/v1/org/org2/sites/siteA/*" in resources
    assert f"{ARN_BASE}/*/*/api/v1/org/org2/sites/siteB/*" in resources


@patch("functions.auth.handler.JWTAuthorizer")
def test_lambda_handler_denies_when_no_orgs(mock_auth_class) -> None:
    mock_auth = Mock()
    mock_auth_class.return_value = mock_auth

    claims = build_claims({})
    mock_auth.validate_token.return_value = claims

    # Mock the event object with the expected attributes
    event = Mock()
    event.authorization_token = "Bearer valid"
    event.method_arn = METHOD_ARN

    policy = lambda_handler(event, Mock())

    assert policy is not None
    assert policy["principalId"] == "unauthorized"
    stmt = policy["policyDocument"]["Statement"][0]
    assert stmt["Effect"] == "Deny"
    assert stmt["Resource"] == METHOD_ARN

