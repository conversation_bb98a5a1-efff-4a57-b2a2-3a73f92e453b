"""Test the fallback handler for unmatched routes."""

import json
from http import HTTPStatus
from unittest.mock import Mock

import pytest

from eyecue_things_api.app import lambda_handler
from eyecue_things_api.context import Context


def create_api_gateway_event(path: str, method: str = "GET") -> dict:
    """Create a mock API Gateway event for testing."""
    return {
        "resource": "/{proxy+}",
        "path": path,
        "httpMethod": method,
        "headers": {
            "Accept": "application/json",
            "Content-Type": "application/json",
        },
        "multiValueHeaders": {},
        "queryStringParameters": None,
        "multiValueQueryStringParameters": None,
        "pathParameters": {"proxy": path.lstrip("/")},
        "stageVariables": None,
        "requestContext": {
            "resourceId": "123456",
            "resourcePath": "/{proxy+}",
            "httpMethod": method,
            "extendedRequestId": "c6af9ac6-7b61-11e6-9a41-93e8deadbeef",
            "requestTime": "09/Apr/2015:12:34:56 +0000",
            "path": "/test/hello",
            "accountId": "**********12",
            "protocol": "HTTP/1.1",
            "stage": "test",
            "domainPrefix": "**********",
            "requestTimeEpoch": *************,
            "requestId": "c6af9ac6-7b61-11e6-9a41-93e8deadbeef",
            "identity": {
                "cognitoIdentityPoolId": None,
                "accountId": None,
                "cognitoIdentityId": None,
                "caller": None,
                "accessKey": None,
                "sourceIp": "127.0.0.1",
                "cognitoAuthenticationType": None,
                "cognitoAuthenticationProvider": None,
                "userArn": None,
                "userAgent": "Custom User Agent String",
                "user": None
            },
            "domainName": "**********.execute-api.us-east-1.amazonaws.com",
            "apiId": "**********"
        },
        "body": None,
        "isBase64Encoded": False
    }


def test_fallback_handler_returns_404_for_unmatched_routes():
    """Test that unmatched routes return 404 Not Found instead of 422 Unprocessable Entity.
    
    This test ensures that the fallback handler fix is working correctly.
    Previously, unmatched routes would return 422 with "Model validation error" message.
    Now they should return 404 with "Not Found" message.
    """
    # Create a mock Lambda context
    lambda_context = Mock()
    lambda_context.function_name = "test-function"
    lambda_context.aws_request_id = "test-request-id"
    
    # Create a mock app context
    app_context = Context()
    
    # Test an unmatched route
    event = create_api_gateway_event("/api/v1/nonexistent/route")
    
    # Call the lambda handler
    response = lambda_handler(event, lambda_context, app_context)
    
    # Verify the response
    assert response["statusCode"] == HTTPStatus.NOT_FOUND.value
    
    # Parse the response body
    body = json.loads(response["body"])
    assert body["message"] == "Not Found"
    assert body["error"] == "Not Found"


def test_fallback_handler_returns_404_for_different_methods():
    """Test that unmatched routes return 404 for different HTTP methods."""
    lambda_context = Mock()
    lambda_context.function_name = "test-function"
    lambda_context.aws_request_id = "test-request-id"
    
    app_context = Context()
    
    # Test different HTTP methods
    for method in ["GET", "POST", "PUT", "DELETE", "PATCH"]:
        event = create_api_gateway_event("/api/v1/nonexistent/route", method)
        response = lambda_handler(event, lambda_context, app_context)
        
        assert response["statusCode"] == HTTPStatus.NOT_FOUND.value
        body = json.loads(response["body"])
        assert body["message"] == "Not Found"


def test_fallback_handler_returns_404_for_root_unmatched_routes():
    """Test that unmatched routes at the root level also return 404."""
    lambda_context = Mock()
    lambda_context.function_name = "test-function"
    lambda_context.aws_request_id = "test-request-id"
    
    app_context = Context()
    
    # Test root level unmatched route
    event = create_api_gateway_event("/nonexistent")
    response = lambda_handler(event, lambda_context, app_context)
    
    assert response["statusCode"] == HTTPStatus.NOT_FOUND.value
    body = json.loads(response["body"])
    assert body["message"] == "Not Found"
    assert body["error"] == "Not Found"
